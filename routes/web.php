<?php

use App\Http\Controllers\LocaleController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Locale switching route
Route::post('locale/{locale}', [LocaleController::class, 'switch'])->name('locale.switch');

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
