<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Locale
    |--------------------------------------------------------------------------
    |
    | This option controls the default locale that will be used by the
    | application. You may change this value to any of the locales
    | which will be supported by your application.
    |
    */

    'default' => env('APP_LOCALE', 'en'),

    /*
    |--------------------------------------------------------------------------
    | Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale determines the locale to use when the current one
    | is not available. You may change the value to any of the locales
    | which will be supported by your application.
    |
    */

    'fallback' => env('APP_FALLBACK_LOCALE', 'en'),

    /*
    |--------------------------------------------------------------------------
    | Available Locales
    |--------------------------------------------------------------------------
    |
    | This array contains all the locales that your application supports.
    | Each locale should have a code, name, native name, flag, and direction.
    |
    */

    'available' => [
        'en' => [
            'code' => 'en',
            'name' => 'English',
            'native' => 'English',
            'flag' => '🇺🇸',
            'emoji_flag' => '🇺🇸',
            'direction' => 'ltr',
            'script' => 'Latn',
            'regional' => 'en_US',
        ],
        'ar' => [
            'code' => 'ar',
            'name' => 'Arabic',
            'native' => 'العربية',
            'flag' => '🇸🇦',
            'emoji_flag' => '🇸🇦',
            'direction' => 'rtl',
            'script' => 'Arab',
            'regional' => 'ar_SA',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | RTL Locales
    |--------------------------------------------------------------------------
    |
    | This array contains all the locales that should be displayed
    | in right-to-left (RTL) direction.
    |
    */

    'rtl' => ['ar'],

    /*
    |--------------------------------------------------------------------------
    | Locale Detection
    |--------------------------------------------------------------------------
    |
    | These options control how the application detects and sets the locale.
    |
    */

    'detection' => [
        // Order of locale detection (first match wins)
        'order' => [
            'request',    // URL parameter or header
            'session',    // Session storage
            'user',       // User preference (if authenticated)
            'browser',    // Browser Accept-Language header
            'default',    // Default locale
        ],

        // Enable automatic locale detection
        'auto_detect' => true,

        // Store locale in session
        'store_in_session' => true,

        // Store locale in user preferences (requires user model modification)
        'store_in_user' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | URL Localization
    |--------------------------------------------------------------------------
    |
    | These options control URL-based localization.
    |
    */

    'url' => [
        // Enable locale in URLs (e.g., /en/dashboard, /ar/dashboard)
        'enabled' => false,

        // Hide default locale from URLs
        'hide_default' => true,

        // URL parameter name for locale switching
        'parameter' => 'locale',
    ],
];
