<?php

namespace App\Providers;

use Illuminate\Support\Facades\App;
use Illuminate\Support\ServiceProvider;

class LocaleServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Set default locale from configuration
        $defaultLocale = config('locale.default', config('app.locale', 'en'));
        App::setLocale($defaultLocale);

        // Register locale configuration as a singleton
        $this->app->singleton('locale.config', function () {
            return config('locale');
        });
    }
}
