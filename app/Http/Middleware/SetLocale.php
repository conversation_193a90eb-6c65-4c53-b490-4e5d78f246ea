<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $availableLocales = array_keys(config('locale.available', []));
        $defaultLocale = config('locale.default', 'en');
        $detectionOrder = config('locale.detection.order', ['request', 'session', 'user', 'browser', 'default']);

        $locale = $defaultLocale;

        // Detect locale based on configured order
        foreach ($detectionOrder as $method) {
            $detectedLocale = match ($method) {
                'request' => $this->getLocaleFromRequest($request, $availableLocales),
                'session' => $this->getLocaleFromSession($availableLocales),
                'user' => $this->getLocaleFromUser($request, $availableLocales),
                'browser' => $this->getLocaleFromBrowser($request, $availableLocales),
                'default' => $defaultLocale,
                default => null,
            };

            if ($detectedLocale && in_array($detectedLocale, $availableLocales)) {
                $locale = $detectedLocale;
                break;
            }
        }

        // Set the application locale
        App::setLocale($locale);

        // Store locale in session if enabled
        if (config('locale.detection.store_in_session', true)) {
            Session::put('locale', $locale);
        }

        return $next($request);
    }

    /**
     * Get locale from request parameter
     */
    private function getLocaleFromRequest(Request $request, array $availableLocales): ?string
    {
        $locale = $request->get('locale') ?? $request->header('X-Locale');

        return in_array($locale, $availableLocales) ? $locale : null;
    }

    /**
     * Get locale from session
     */
    private function getLocaleFromSession(array $availableLocales): ?string
    {
        $locale = Session::get('locale');

        return in_array($locale, $availableLocales) ? $locale : null;
    }

    /**
     * Get locale from authenticated user preferences
     */
    private function getLocaleFromUser(Request $request, array $availableLocales): ?string
    {
        if ($request->user() && isset($request->user()->locale)) {
            $locale = $request->user()->locale;
            return in_array($locale, $availableLocales) ? $locale : null;
        }

        return null;
    }

    /**
     * Get locale from browser Accept-Language header
     */
    private function getLocaleFromBrowser(Request $request, array $availableLocales): ?string
    {
        $acceptLanguage = $request->header('Accept-Language');

        if (!$acceptLanguage) {
            return null;
        }

        // Parse Accept-Language header
        $languages = [];
        foreach (explode(',', $acceptLanguage) as $lang) {
            $parts = explode(';', trim($lang));
            $code = trim($parts[0]);
            $quality = 1.0;

            if (isset($parts[1]) && strpos($parts[1], 'q=') === 0) {
                $quality = (float) substr($parts[1], 2);
            }

            $languages[$code] = $quality;
        }

        // Sort by quality
        arsort($languages);

        // Find the best matching locale
        foreach ($languages as $lang => $quality) {
            // Check exact match
            if (in_array($lang, $availableLocales)) {
                return $lang;
            }

            // Check language code only (e.g., 'ar' from 'ar-SA')
            $langCode = substr($lang, 0, 2);
            if (in_array($langCode, $availableLocales)) {
                return $langCode;
            }
        }

        return null;
    }
}
