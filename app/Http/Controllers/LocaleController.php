<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class LocaleController extends Controller
{
    /**
     * Switch the application locale
     */
    public function switch(Request $request, string $locale): RedirectResponse
    {
        $availableLocales = ['en', 'ar'];
        
        if (!in_array($locale, $availableLocales)) {
            abort(400, 'Invalid locale');
        }

        // Store locale in session
        Session::put('locale', $locale);

        // If user is authenticated, optionally store in user preferences
        if ($request->user()) {
            // Uncomment if you add locale column to users table
            // $request->user()->update(['locale' => $locale]);
        }

        return redirect()->back();
    }
}
