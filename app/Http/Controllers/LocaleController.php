<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class LocaleController extends Controller
{
    /**
     * Switch the application locale
     */
    public function switch(Request $request, string $locale): RedirectResponse
    {
        $availableLocales = array_keys(config('locale.available', []));

        if (!in_array($locale, $availableLocales)) {
            abort(400, 'Invalid locale');
        }

        // Store locale in session if enabled
        if (config('locale.detection.store_in_session', true)) {
            Session::put('locale', $locale);
        }

        // If user is authenticated and user storage is enabled, store in user preferences
        if ($request->user() && config('locale.detection.store_in_user', false)) {
            // Uncomment if you add locale column to users table
            // $request->user()->update(['locale' => $locale]);
        }

        return redirect()->back();
    }
}
