<script setup lang="ts">
import { useLocale } from '@/composables/useLocale';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import Icon from '@/components/Icon.vue';

const { currentLocale, availableLocales, switchLocale, getLocaleDisplayName } = useLocale();
</script>

<template>
    <DropdownMenu>
        <DropdownMenuTrigger as-child>
            <Button variant="ghost" size="sm" class="h-8 w-8 px-0">
                <Icon name="languages" class="h-4 w-4" />
                <span class="sr-only">{{ $t('general.language') }}</span>
            </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
            <DropdownMenuItem
                v-for="(name, locale) in availableLocales"
                :key="locale"
                @click="switchLocale(locale)"
                :class="{
                    'bg-accent text-accent-foreground': currentLocale === locale,
                }"
            >
                <div class="flex items-center gap-2">
                    <span class="text-sm">{{ name }}</span>
                    <Icon
                        v-if="currentLocale === locale"
                        name="check"
                        class="h-3 w-3"
                    />
                </div>
            </DropdownMenuItem>
        </DropdownMenuContent>
    </DropdownMenu>
</template>
