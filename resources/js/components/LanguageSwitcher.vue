<script setup lang="ts">
import Icon from '@/components/Icon.vue';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useLocale } from '@/composables/useLocale';
import { usePage } from '@inertiajs/vue3';

const page = usePage();
const { currentLocale, availableLocales, switchLocale, currentLocaleFlag } = useLocale();

// Get locale configuration data
const allLocalesConfig = {
    en: { flag: '🇺🇸', native: 'English' },
    ar: { flag: '🇸🇦', native: 'العربية' },
};

const getLocaleFlag = (locale: string) => {
    return allLocalesConfig[locale as keyof typeof allLocalesConfig]?.flag || '🌐';
};
</script>

<template>
    <DropdownMenu>
        <DropdownMenuTrigger as-child>
            <Button variant="ghost" size="sm" class="h-8 gap-1 px-2">
                <span class="text-sm">{{ currentLocaleFlag }}</span>
                <Icon name="chevron-down" class="h-3 w-3" />
                <span class="sr-only">Language</span>
            </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
            <DropdownMenuItem
                v-for="(name, locale) in availableLocales"
                :key="locale"
                @click="switchLocale(locale)"
                :class="{
                    'bg-accent text-accent-foreground': currentLocale === locale,
                }"
            >
                <div class="flex items-center gap-2">
                    <span class="text-sm">{{ getLocaleFlag(locale) }}</span>
                    <span class="text-sm">{{ name }}</span>
                    <Icon v-if="currentLocale === locale" name="check" class="ml-auto h-3 w-3" />
                </div>
            </DropdownMenuItem>
        </DropdownMenuContent>
    </DropdownMenu>
</template>
