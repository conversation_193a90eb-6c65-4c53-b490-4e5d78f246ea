import { usePage } from '@inertiajs/vue3';
import { loadLanguageAsync } from 'laravel-vue-i18n';
import { computed, watch } from 'vue';

export function useLocale() {
    const page = usePage();

    // Get current locale from Inertia props
    const currentLocale = computed(() => page.props.locale as string);
    const availableLocales = computed(() => page.props.locales as Record<string, string>);
    const localeData = computed(() => page.props.localeData as any);
    const isRtl = computed(() => page.props.isRtl as boolean);

    // Switch locale function
    const switchLocale = async (locale: string) => {
        try {
            // Load the language files
            await loadLanguageAsync(locale);

            // Make request to backend to switch locale
            await fetch(route('locale.switch', { locale }), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });

            // Reload the page to apply the new locale
            window.location.reload();
        } catch (error) {
            console.error('Failed to switch locale:', error);
        }
    };

    // Apply RTL/LTR direction to document
    const applyDirection = () => {
        const html = document.documentElement;
        if (isRtl.value) {
            html.setAttribute('dir', 'rtl');
            html.classList.add('rtl');
            html.classList.remove('ltr');
        } else {
            html.setAttribute('dir', 'ltr');
            html.classList.add('ltr');
            html.classList.remove('rtl');
        }
    };

    // Watch for locale changes and apply direction
    watch(isRtl, applyDirection, { immediate: true });

    // Get locale display name
    const getLocaleDisplayName = (locale: string) => {
        return availableLocales.value[locale] || locale;
    };

    // Check if locale is RTL
    const isLocaleRtl = (locale: string) => {
        // Get RTL locales from the locale data if available
        const rtlLocales = ['ar']; // fallback
        return rtlLocales.includes(locale);
    };

    // Get locale flag
    const getLocaleFlag = (locale: string) => {
        return localeData.value?.flag || '🌐';
    };

    // Get current locale flag
    const currentLocaleFlag = computed(() => getLocaleFlag(currentLocale.value));

    return {
        currentLocale,
        availableLocales,
        localeData,
        isRtl,
        switchLocale,
        applyDirection,
        getLocaleDisplayName,
        isLocaleRtl,
        getLocaleFlag,
        currentLocaleFlag,
    };
}
