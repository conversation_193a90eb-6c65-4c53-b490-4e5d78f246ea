# Internationalization (i18n) Setup

This project has been configured with internationalization support using the `xiCO2k/laravel-vue-i18n` package, supporting English (EN) and Arabic (AR) locales with RTL support.

## Features

- ✅ English and Arabic language support
- ✅ RTL (Right-to-Left) layout support for Arabic
- ✅ Automatic locale detection from browser, session, or user preferences
- ✅ Language switcher component with flags
- ✅ Centralized locale configuration
- ✅ Laravel backend integration
- ✅ Vue.js frontend integration with Inertia.js

## Configuration

### Locale Configuration (`config/locale.php`)

The main configuration file contains:
- Available locales with metadata (name, native name, flag, direction)
- RTL locale definitions
- Locale detection settings
- URL localization options

### Available Locales

- **English (en)**: 🇺🇸 English - LTR
- **Arabic (ar)**: 🇸🇦 العربية - RTL

## Usage

### In Vue Components

```vue
<template>
  <div>
    <h1>{{ $t('general.welcome') }}</h1>
    <p>{{ $t('nav.dashboard') }}</p>
  </div>
</template>
```

### In Laravel Blade Templates

```blade
<h1>{{ __('general.welcome') }}</h1>
<p>@lang('nav.dashboard')</p>
```

### Language Switching

The `LanguageSwitcher` component is available and can be used anywhere:

```vue
<LanguageSwitcher />
```

## Translation Files

Translation files are located in `lang/{locale}/`:

- `lang/en/` - English translations
- `lang/ar/` - Arabic translations

### File Structure

- `general.php` - Common UI elements
- `auth.php` - Authentication related strings
- `nav.php` - Navigation and menu items

## RTL Support

RTL support is automatically applied when Arabic locale is selected:

- CSS classes are automatically adjusted
- Direction attribute is set on the HTML element
- Custom RTL utilities are available in CSS

### RTL CSS Classes

The following RTL-aware utilities are available:

- `.rtl .ml-auto` → `margin-right: auto`
- `.rtl .text-left` → `text-align: right`
- `.rtl .border-l` → `border-right`
- And many more...

## Locale Detection

The system detects locale in the following order:

1. Request parameter (`?locale=ar`)
2. Session storage
3. User preferences (if authenticated)
4. Browser Accept-Language header
5. Default locale (en)

## Adding New Locales

1. Add locale configuration to `config/locale.php`
2. Create translation files in `lang/{locale}/`
3. Update RTL array if the locale is RTL
4. Add flag and metadata

## Components

### LanguageSwitcher

A dropdown component that allows users to switch between available locales.

**Props**: None
**Features**:
- Shows current locale flag
- Lists all available locales with flags
- Handles locale switching
- Responsive design

### useLocale Composable

A Vue composable that provides locale-related functionality:

```typescript
const {
  currentLocale,
  availableLocales,
  isRtl,
  switchLocale,
  currentLocaleFlag
} = useLocale();
```

## Middleware

### SetLocale

Automatically detects and sets the application locale based on configuration.

**Features**:
- Multiple detection methods
- Session persistence
- User preference support
- Browser language detection

## Development

### Building Assets

```bash
npm run build
```

### Adding Translations

1. Add keys to English files first
2. Add corresponding translations to Arabic files
3. Maintain consistent key structure across all locales

## File Structure

```
├── config/
│   └── locale.php                 # Main locale configuration
├── lang/
│   ├── en/                       # English translations
│   │   ├── general.php
│   │   ├── auth.php
│   │   └── nav.php
│   └── ar/                       # Arabic translations
│       ├── general.php
│       ├── auth.php
│       └── nav.php
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   └── LocaleController.php
│   │   └── Middleware/
│   │       └── SetLocale.php
│   └── Providers/
│       └── LocaleServiceProvider.php
└── resources/
    ├── js/
    │   ├── components/
    │   │   └── LanguageSwitcher.vue
    │   └── composables/
    │       └── useLocale.ts
    └── css/
        └── app.css               # RTL CSS utilities
```

## Notes

- The system is designed to be easily extensible for additional locales
- RTL support is comprehensive and handles most common UI patterns
- Translation keys follow a hierarchical structure for better organization
- The setup integrates seamlessly with existing Laravel and Vue.js patterns
